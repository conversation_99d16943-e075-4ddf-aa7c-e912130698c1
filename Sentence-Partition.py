#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用本地 facebook/xlm-roberta-xl 模型进行句子分割和对齐
功能：
- 对英文句子列表和中文句子列表进行双端收缩匹配
- 使用本地模型进行分词和词嵌入
- 计算双向一致的对齐词汇对和词覆盖率
运行环境:
  pip install transformers torch numpy
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModel
import unicodedata

# 固定的编码层号常量
LAYER_NUM = 8

print(torch.cuda.is_available())      # 应返回 True
print(torch.version.cuda)             # 应显示 CUDA 版本号

print("\n" + "="*60)
# 检测与显示设备信息
if torch.cuda.is_available():
    device_str = f"cuda:{torch.cuda.current_device()}"
    gpu_name = torch.cuda.get_device_name(torch.cuda.current_device())
    print(f"[Device] Using GPU: {device_str} - {gpu_name}")
    device = torch.device(device_str)
else:
    device_str = "cpu"
    print(f"[Device] Using CPU")
    device = torch.device("cpu")

print("\n" + "="*60)
print("模型加载")
print("="*60)

model_id = "facebook/xlm-roberta-xl"

# 加载 tokenizer 和模型
print(f"[模型信息] 正在加载模型: {model_id}")
tokenizer = AutoTokenizer.from_pretrained(model_id)
model = AutoModel.from_pretrained(model_id, trust_remote_code=True, add_pooling_layer=False)
model = model.to(device)
model.eval()

# 获取模型配置信息
if hasattr(model, 'config'):
    print(f"[模型信息] 总层数: {getattr(model.config, 'num_hidden_layers', 'N/A')}")
    print(f"[模型信息] 隐藏层维度: {getattr(model.config, 'hidden_size', 'N/A')}")

print("\n" + "="*60)


def get_layer_embeddings(model, tokenizer, sentences):
    """
    获取指定层（常量 LAYER_NUM）的词嵌入向量

    Args:
        model: 模型实例
        tokenizer: 对应的 tokenizer
        sentences: 句子列表或单个句子字符串

    Returns:
        embeddings: 词嵌入向量列表
        tokens_list: 分词结果列表
    """
    # 确保输入是列表格式
    if isinstance(sentences, str):
        sentences = [sentences]

    embeddings = []
    tokens_list = []

    for sentence in sentences:
        # 分词
        inputs = tokenizer(sentence, return_tensors="pt", padding=False, truncation=True, max_length=512)
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # 获取分词结果
        tokens = tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        tokens_list.append(tokens)

        # 获取模型输出，包括所有隐藏层
        with torch.no_grad():
            outputs = model(**inputs, output_hidden_states=True)

        # 提取常量层的输出（不做越界检查，遵循你的偏好）
        # hidden_states[0] 为输入嵌入，第 1..N 为第 1..N 层输出
        layer_output = outputs.hidden_states[LAYER_NUM]  # shape: [batch_size, seq_len, hidden_size]
        embeddings.append(layer_output[0])  # 取第一个（也是唯一的）batch

    return embeddings, tokens_list


def decode_token(token: str) -> str:
    """将单个 token 解码为可读文本（去除多余空白）。"""
    try:
        s = tokenizer.convert_tokens_to_string([token]).strip()
        return s if s != "" else token
    except Exception:
        return token


def is_special_token(token: str) -> bool:
    """是否为分词器生成的特殊标记，如 <s>、</s>、<pad> 等。"""
    if token in getattr(tokenizer, 'all_special_tokens', []):
        return True
    if token.startswith('<') and token.endswith('>'):
        return True
    # 若解码为空字符串，则视为无用标记
    try:
        if tokenizer.convert_tokens_to_string([token]).strip() == "":
            return True
    except Exception:
        pass
    return False


def is_punct_or_symbol_text(text: str) -> bool:
    """判断一个解码后的文本是否全为标点/符号。"""
    if not text:
        return False
    for ch in text:
        cat = unicodedata.category(ch)
        if not (cat.startswith('P') or cat.startswith('S')):
            return False
    return True


def aggregate_english_tokens_to_words(tokens: list, embedding_tensor: torch.Tensor):
    """
    将英文 SPM 子词聚合为"词"级别，并保留标点为独立单元；
    返回：words(list[str]), spans(list[list[int]]), pooled_vecs(torch.Tensor[num_words, hidden])
    """
    spans = []
    words = []
    current_span = []

    def flush_span():
        nonlocal current_span
        if current_span:
            spans.append(current_span)
            # 以 span 内 token 还原文本（用于显示）
            token_text = tokenizer.convert_tokens_to_string([tokens[i] for i in current_span]).strip()
            words.append(token_text if token_text else ''.join([decode_token(tokens[i]) for i in current_span]))
            current_span = []

    for i, tok in enumerate(tokens):
        if is_special_token(tok):
            flush_span()
            continue
        text = decode_token(tok)
        # 遇到纯标点：作为独立单元
        if is_punct_or_symbol_text(text):
            flush_span()
            spans.append([i])
            words.append(text)
            continue

        if tok.startswith('▁'):
            # 新词开始
            flush_span()
            current_span = [i]
        else:
            if not current_span:
                current_span = [i]
            else:
                current_span.append(i)

    flush_span()

    # 平均池化
    if len(spans) == 0:
        pooled = torch.empty((0, embedding_tensor.shape[-1]), device=embedding_tensor.device)
    else:
        pooled = []
        for span in spans:
            valid = [j for j in span if j < embedding_tensor.shape[0]]
            if not valid:
                pooled.append(torch.zeros((embedding_tensor.shape[-1],), device=embedding_tensor.device))
            else:
                pooled.append(embedding_tensor[valid].mean(dim=0))
        pooled = torch.stack(pooled, dim=0)
    return words, spans, pooled


def filter_tokens_keep_punct(tokens: list, embedding_tensor: torch.Tensor):
    """
    中文路径：仅移除分词器特殊标记，保留原始标点；
    返回：texts(list[str]), kept_indices(list[int]), kept_vecs(torch.Tensor[num, hidden])
    """
    kept_indices = [i for i, tok in enumerate(tokens) if not is_special_token(tok)]
    texts = [decode_token(tokens[i]) for i in kept_indices]
    if len(kept_indices) == 0:
        kept_vecs = torch.empty((0, embedding_tensor.shape[-1]), device=embedding_tensor.device)
    else:
        kept_vecs = embedding_tensor[kept_indices]
    return texts, kept_indices, kept_vecs


def compute_word_alignment(embeddings_en, embeddings_zh, tokens_en, tokens_zh):
    """
    计算中英文句子的词级别对齐和相似度（英文侧按"单词"聚合）。

    Args:
        embeddings_en: 英文句子的子词级嵌入 (seq_len, hidden)
        embeddings_zh: 中文句子的子词级嵌入 (seq_len, hidden)
        tokens_en: 英文分词结果（子词列表，或 [子词列表] 的嵌套）
        tokens_zh: 中文分词结果（子词列表，或 [子词列表] 的嵌套）

    Returns:
        alignment_matrix: 词对齐相似度矩阵（英文词 × 中文 token）
        words_en: 英文单词列表（与矩阵行对应）
        tokens_zh: 中文子词列表（与矩阵列对应）
    """
    # 获取子词级向量
    vec_en = embeddings_en[0] if isinstance(embeddings_en, list) else embeddings_en
    vec_zh = embeddings_zh[0] if isinstance(embeddings_zh, list) else embeddings_zh

    # 转换为 numpy 数组
    if isinstance(vec_en, torch.Tensor):
        vec_en = vec_en.cpu().numpy()
    if isinstance(vec_zh, torch.Tensor):
        vec_zh = vec_zh.cpu().numpy()

    # 展平 tokens 列表
    tokens_en_sub = tokens_en[0] if isinstance(tokens_en[0], list) else tokens_en
    tokens_zh = tokens_zh[0] if isinstance(tokens_zh[0], list) else tokens_zh

    def is_punct_text(text: str) -> bool:
        if not text:
            return False
        for ch in text:
            cat = unicodedata.category(ch)
            if not (cat.startswith('P') or cat.startswith('S')):
                return False
        return True

    # 英文：聚合为词并保留标点为独立单元
    words_en = []
    word_spans_en = []
    current_span = []

    def flush_span():
        nonlocal current_span
        if current_span:
            word_spans_en.append(current_span)
            token_text = tokenizer.convert_tokens_to_string([tokens_en_sub[i] for i in current_span]).strip()
            words_en.append(token_text if token_text else ''.join([decode_token(tokens_en_sub[i]) for i in current_span]))
            current_span = []

    for idx, tok in enumerate(tokens_en_sub):
        if is_special_token(tok):
            flush_span()
            continue
        text = decode_token(tok)
        if is_punct_text(text):
            flush_span()
            word_spans_en.append([idx])
            words_en.append(text)
            continue
        if tok.startswith('▁'):
            flush_span()
            current_span = [idx]
        else:
            if not current_span:
                current_span = [idx]
            else:
                current_span.append(idx)
    flush_span()

    # 将英文子词向量聚合为词级向量（平均池化）
    en_word_vecs = []
    hidden_size = vec_en.shape[1] if vec_en.ndim == 2 else (vec_zh.shape[1] if vec_zh.ndim == 2 else 0)
    for span in word_spans_en:
        valid_indices = [i for i in span if i < vec_en.shape[0]]
        if not valid_indices:
            en_word_vecs.append(np.zeros(hidden_size, dtype=np.float32))
            continue
        vecs = vec_en[valid_indices]
        en_word_vecs.append(vecs.mean(axis=0))

    # 中文：仅移除特殊标记，保留原标点
    zh_keep_indices = [j for j, t in enumerate(tokens_zh) if not is_special_token(str(t))]
    tokens_zh_filtered = [decode_token(tokens_zh[j]) for j in zh_keep_indices]
    zh_vecs = vec_zh[zh_keep_indices] if len(zh_keep_indices) > 0 else np.zeros((0, vec_zh.shape[1]))

    # 计算"英文词 × 中文 token"的相似度矩阵（先进行 L2 归一化，再用点积，等价于余弦相似度）
    n_tokens_en = len(en_word_vecs)
    n_tokens_zh = zh_vecs.shape[0]
    if n_tokens_en == 0 or n_tokens_zh == 0:
        alignment_matrix = np.zeros((n_tokens_en, n_tokens_zh))
        return alignment_matrix, words_en, tokens_zh_filtered

    en_mat = np.vstack(en_word_vecs).astype(np.float32)  # [n_en, hidden]
    zh_mat = zh_vecs.astype(np.float32)                  # [n_zh, hidden]

    # L2 归一化
    en_norms = np.linalg.norm(en_mat, axis=1, keepdims=True)
    zh_norms = np.linalg.norm(zh_mat, axis=1, keepdims=True)
    en_mat = en_mat / np.clip(en_norms, 1e-8, None)
    zh_mat = zh_mat / np.clip(zh_norms, 1e-8, None)

    # 点积 = 归一化后的余弦相似度
    alignment_matrix = en_mat @ zh_mat.T

    return alignment_matrix, words_en, tokens_zh_filtered


def compute_softmax_alignments(alignment_matrix):
    """
    对对齐矩阵分别按行和按列进行Softmax归一化

    Args:
        alignment_matrix: 词对齐相似度矩阵 (n_en_words, n_zh_tokens)

    Returns:
        softmax_en2zh: 按行Softmax - 英文词对中文token的概率分布 (每行和为1)
        softmax_zh2en: 按列Softmax - 中文token对英文词的概率分布 (每列和为1)
    """
    if alignment_matrix.size == 0:
        return alignment_matrix.copy(), alignment_matrix.copy()

    # 数值稳定的Softmax实现
    def stable_softmax(x, axis=None):
        """数值稳定的Softmax计算"""
        x_max = np.max(x, axis=axis, keepdims=True)
        exp_x = np.exp(x - x_max)
        sum_exp_x = np.sum(exp_x, axis=axis, keepdims=True)
        return exp_x / sum_exp_x

    # 按行Softmax: 英文词→中文token的概率分布
    softmax_en2zh = stable_softmax(alignment_matrix, axis=1)

    # 按列Softmax: 中文token→英文词的概率分布
    softmax_zh2en = stable_softmax(alignment_matrix, axis=0)

    return softmax_en2zh, softmax_zh2en


def compute_bidirectional_alignment(softmax_en2zh, softmax_zh2en, tokens_en, tokens_zh):
    """
    计算双向最大概率对齐的共同结果

    Args:
        softmax_en2zh: 英文词对中文token的概率分布矩阵
        softmax_zh2en: 中文token对英文词的概率分布矩阵
        tokens_en: 英文分词结果
        tokens_zh: 中文分词结果

    Returns:
        bidirectional_pairs: 双向一致对齐的词汇对列表 [(en_idx, zh_idx, prob_en2zh, prob_zh2en)]
    """
    bidirectional_pairs = []

    # 遍历所有英文词
    for i, token_en in enumerate(tokens_en):
        if i < softmax_en2zh.shape[0]:
            # 找出英文词i的最佳中文对齐
            best_zh_idx = np.argmax(softmax_en2zh[i, :])
            prob_en2zh = softmax_en2zh[i, best_zh_idx]

            # 检查该中文词的最佳英文对齐是否指向当前英文词
            if best_zh_idx < softmax_zh2en.shape[1]:
                best_en_idx = np.argmax(softmax_zh2en[:, best_zh_idx])
                prob_zh2en = softmax_zh2en[best_en_idx, best_zh_idx]

                # 如果双向互为最佳对齐，则记录
                if best_en_idx == i:
                    bidirectional_pairs.append((i, best_zh_idx, prob_en2zh, prob_zh2en))

    return bidirectional_pairs


def calculate_word_coverage(english_sentence, chinese_sentence):
    """
    计算英文句子的词覆盖率

    Args:
        english_sentence: 英文句子字符串
        chinese_sentence: 中文句子字符串

    Returns:
        coverage: 词覆盖率 (0.0 到 1.0)
        bidirectional_pairs: 双向一致对齐的词汇对
    """
    # 获取词嵌入和分词结果
    embeddings_en, tokens_en = get_layer_embeddings(model, tokenizer, english_sentence)
    embeddings_zh, tokens_zh = get_layer_embeddings(model, tokenizer, chinese_sentence)

    # 计算词级别对齐
    alignment_matrix, words_en, tokens_zh_flat = compute_word_alignment(
        embeddings_en, embeddings_zh, tokens_en, tokens_zh
    )

    # 计算Softmax概率分布
    softmax_en2zh, softmax_zh2en = compute_softmax_alignments(alignment_matrix)

    # 计算双向一致对齐
    bidirectional_pairs = compute_bidirectional_alignment(softmax_en2zh, softmax_zh2en, words_en, tokens_zh_flat)

    # 计算词覆盖率：分母是对应英文句子的词汇数，分子是双语一致性词汇数
    threshold = 0.15

    # 计算英文词汇数和中文词汇数
    total_en_words = len(words_en)
    total_zh_words = len(tokens_zh_flat)

    # 显示参与计算的值
    print(f"    [覆盖率计算] 英文词汇数: {total_en_words}, 中文词汇数: {total_zh_words}")
    print(f"    [覆盖率计算] 阈值: {threshold}")

    # 计算双语一致性词汇数并显示详细信息
    matched_pairs_count = 0
    consistent_pairs = []

    print(f"    [一致性分析] 双向一致对齐词汇对:")
    for en_idx, zh_idx, p_en, p_zh in bidirectional_pairs:
        avg_prob = (p_en + p_zh) / 2
        is_consistent = avg_prob > threshold

        en_word = words_en[en_idx] if en_idx < len(words_en) else "N/A"
        zh_word = tokens_zh_flat[zh_idx] if zh_idx < len(tokens_zh_flat) else "N/A"

        status = "✓" if is_consistent else "✗"
        print(f"      {status} '{en_word}' <-> '{zh_word}' (英→中: {p_en:.3f}, 中→英: {p_zh:.3f}, 平均: {avg_prob:.3f})")

        if is_consistent:
            matched_pairs_count += 1
            consistent_pairs.append((en_word, zh_word, avg_prob))

    print(f"    [一致性结果] 符合阈值的对齐数: {matched_pairs_count}/{len(bidirectional_pairs)}")

    # 新的覆盖率计算：双语一致性词汇数 / 对应英文句子的词汇数
    coverage = (matched_pairs_count / total_en_words) if total_en_words > 0 else 0.0

    print(f"    [覆盖率结果] {matched_pairs_count}/{total_en_words} = {coverage:.3f}")

    return coverage, bidirectional_pairs





def find_optimal_segmentation(english_sentences, chinese_long_text):
    """
    找到中文长句子的最佳分割位置，使得整体词覆盖率最大

    Args:
        english_sentences: 英文句子列表 (2个或3个句子)
        chinese_long_text: 中文长句子字符串

    Returns:
        best_segmentation: 最佳分割结果
    """
    print(f"\n{'='*60}")
    print("寻找中文长句子的最佳分割位置")
    print(f"{'='*60}")

    print(f"英文句子数: {len(english_sentences)}")
    print(f"中文长句子: {chinese_long_text}")

    # 使用本地模型对中文长句子进行分词
    embeddings_zh, tokens_zh = get_layer_embeddings(model, tokenizer, chinese_long_text)
    chinese_tokens = tokens_zh[0]  # 获取分词结果

    # 过滤特殊标记，保留实际的中文分词
    chinese_filtered_tokens = []
    for i, token in enumerate(chinese_tokens):
        if not is_special_token(token):
            chinese_filtered_tokens.append(decode_token(token))

    print(f"中文分词结果: {chinese_filtered_tokens}")
    print(f"分词数量: {len(chinese_filtered_tokens)}")

    num_english = len(english_sentences)
    num_chinese_tokens = len(chinese_filtered_tokens)

    if num_english == 2:
        return find_best_two_way_split(english_sentences, chinese_filtered_tokens)
    else:
        print(f"错误：只支持2个英文句子，当前有{num_english}个句子")
        return None


def find_best_two_way_split(english_sentences, chinese_tokens):
    """
    分别找到英文第一句和最后一句的最佳词覆盖率
    """
    print(f"\n{'='*50}")
    print("分别寻找英文第一句和最后一句的最佳词覆盖率")
    print(f"{'='*50}")

    # 为第一个英文句子寻找最佳覆盖率
    print(f"\n为英文第一句寻找最佳覆盖率: '{english_sentences[0]}'")
    best_coverage1 = 0.0
    best_split_for_first = None
    best_segment1 = None
    best_pairs1 = None

    for split_pos in range(1, len(chinese_tokens)):
        print(f"\n    {'='*50}")
        print(f"    分割位置 {split_pos}: 从头部取 {split_pos} 个分词")
        print(f"    {'='*50}")

        chinese_part1 = ''.join(chinese_tokens[:split_pos])
        print(f"    候选片段: '{chinese_part1}'")

        coverage1, pairs1 = calculate_word_coverage(english_sentences[0], chinese_part1)

        print(f"    最终覆盖率: {coverage1:.3f}")

        if coverage1 > best_coverage1:
            best_coverage1 = coverage1
            best_split_for_first = split_pos
            best_segment1 = chinese_part1
            best_pairs1 = pairs1

    print(f"\n英文第一句最佳覆盖率:")
    print(f"  分割位置: {best_split_for_first}")
    print(f"  覆盖率: {best_coverage1:.3f}")
    print(f"  匹配片段: '{best_segment1}'")

    # 为第二个英文句子寻找最佳覆盖率（从尾部向前增加）
    print(f"\n为英文最后一句寻找最佳覆盖率: '{english_sentences[1]}'")
    best_coverage2 = 0.0
    best_length_for_second = None
    best_segment2 = None
    best_pairs2 = None

    # 从尾部开始，逐步向前增加分词长度
    for length in range(1, len(chinese_tokens) + 1):
        print(f"\n    {'='*50}")
        print(f"    尾部长度 {length}: 从尾部取 {length} 个分词")
        print(f"    {'='*50}")

        chinese_part2 = ''.join(chinese_tokens[-length:])  # 从尾部取length个分词
        print(f"    候选片段: '{chinese_part2}'")

        coverage2, pairs2 = calculate_word_coverage(english_sentences[1], chinese_part2)

        print(f"    最终覆盖率: {coverage2:.3f}")

        if coverage2 > best_coverage2:
            best_coverage2 = coverage2
            best_length_for_second = length
            best_segment2 = chinese_part2
            best_pairs2 = pairs2

    print(f"\n英文最后一句最佳覆盖率:")
    print(f"  尾部长度: {best_length_for_second}")
    print(f"  覆盖率: {best_coverage2:.3f}")
    print(f"  匹配片段: '{best_segment2}'")

    result = {
        'first_sentence': {
            'split_position': best_split_for_first,
            'segment': best_segment1,
            'coverage': best_coverage1,
            'pairs': best_pairs1
        },
        'second_sentence': {
            'tail_length': best_length_for_second,
            'segment': best_segment2,
            'coverage': best_coverage2,
            'pairs': best_pairs2
        }
    }

    return result











def main():
    # 原始测试用例：2个句子
    print(f"使用 {model_id} 模型第 {LAYER_NUM} 层寻找最佳分割位置")

    english_sentences = [
        "in which the everyday citizens in these communities",
        "contribute to the projects that are in the campaign"
    ]

    chinese_long_text = "由社区里的普通市民为这些活动中的项目出资"

    print(f"\n{'='*60}")
    print("测试用例1：2个句子")
    print(f"{'='*60}")
    print(f"英文句子列表: {english_sentences}")
    print(f"中文长句子: {chinese_long_text}")

    # 寻找最佳分割位置
    result = find_optimal_segmentation(english_sentences, chinese_long_text)

    if result:
        print(f"\n{'='*60}")
        print("2句子最佳覆盖率结果")
        print(f"{'='*60}")

        print(f"英文第一句: '{english_sentences[0]}'")
        print(f"  最佳分割位置: {result['first_sentence']['split_position']}")
        print(f"  最佳匹配片段: '{result['first_sentence']['segment']}'")
        print(f"  最佳覆盖率: {result['first_sentence']['coverage']:.3f}")

        print(f"\n英文最后一句: '{english_sentences[1]}'")
        print(f"  最佳尾部长度: {result['second_sentence']['tail_length']}")
        print(f"  最佳匹配片段: '{result['second_sentence']['segment']}'")
        print(f"  最佳覆盖率: {result['second_sentence']['coverage']:.3f}")


if __name__ == "__main__":
    main()