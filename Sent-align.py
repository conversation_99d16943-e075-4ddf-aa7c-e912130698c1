from hanlp_restful import HanLPClient
import requests
import numpy as np
from scipy.spatial.distance import cosine


def get_embeddings(text_list, api_url, api_key):
    """获取文本列表的嵌入向量"""
    if not text_list:
        return []

    embeddings = []

    for text in text_list:
        payload = {
            "model": "BAAI/bge-m3",
            "input": text,
            "encoding_format": "float"
        }

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        response = requests.request("POST", api_url, json=payload, headers=headers)
        response_data = response.json()

        # 创建一个类似于原有结构的嵌入对象
        class EmbeddingObject:
            def __init__(self, values):
                self.values = values

        # 根据API响应结构提取嵌入向量
        if 'data' in response_data and len(response_data['data']) > 0:
            vector = response_data['data'][0]['embedding']
            embeddings.append(EmbeddingObject(vector))
        else:
            print(f"警告: 无法获取文本的嵌入向量: '{text}'")
            print(f"响应: {response_data}")
            embeddings.append(EmbeddingObject([]))

    return embeddings


def cosine_similarity(vec1, vec2):
    """计算两个向量之间的余弦相似度"""
    if not vec1 or not vec2:
        return 0
    return 1 - cosine(vec1, vec2)


def calculate_similarity_matrix(embeddings1, embeddings2):
    """计算两组嵌入向量之间的余弦相似度矩阵"""
    similarity_matrix = np.zeros((len(embeddings1), len(embeddings2)))

    for i, emb1 in enumerate(embeddings1):
        for j, emb2 in enumerate(embeddings2):
            similarity_matrix[i, j] = cosine_similarity(emb1.values, emb2.values)

    return similarity_matrix


def find_optimal_split_enhanced(result, forward_candidates, backward_candidates,
                                forward_english_sim, backward_english_sim):
    """
    找到文本的最优分割点

    参数:
        result: 分词后的文本
        forward_candidates: 前向候选子句列表
        backward_candidates: 后向候选子句列表
        forward_english_sim: 前向候选与英文第一句的相似度矩阵
        backward_english_sim: 后向候选与英文第二句的相似度矩阵

    返回:
        元组 (split_index, forward_index, backward_index, scores)
        其中scores是一个包含各项得分的字典
    """
    n = len(result)
    best_total_score = -1
    best_split = None
    best_scores = None

    # 尝试所有可能的分割点
    for split_idx in range(1, n):
        # 获取当前分割点的前向和后向部分
        forward_tokens = result[:split_idx]
        backward_tokens = result[split_idx:]

        # 如果任一部分太短（少于2个token），则跳过
        if len(forward_tokens) < 2 or len(backward_tokens) < 2:
            continue

        # 找到对应的候选索引
        forward_idx = len(forward_tokens) - 2  # 前向候选从长度2开始，0索引
        backward_idx = len(backward_tokens) - 2  # 后向候选从长度2开始，0索引

        # 如果索引超出范围，则跳过
        if (forward_idx < 0 or forward_idx >= len(forward_english_sim) or
                backward_idx < 0 or backward_idx >= len(backward_english_sim)):
            continue

        # 计算各项得分
        forward_score = forward_english_sim[forward_idx, 0]
        backward_score = backward_english_sim[backward_idx, 0]

        # 计算总得分
        total_score = forward_score + backward_score

        # 如果得分更高，则更新最佳分割点
        if total_score > best_total_score:
            best_total_score = total_score
            best_split = (split_idx, forward_idx, backward_idx)
            best_scores = {
                'forward_score': forward_score,
                'backward_score': backward_score,
                'total_score': total_score
            }

    if best_split:
        return best_split[0], best_split[1], best_split[2], best_scores
    else:
        return None, None, None, None


def print_embeddings(embeddings, text_list, type_name):
    """以格式化方式打印嵌入向量"""
    print(f"\n{type_name}的嵌入向量:")

    for i, embedding in enumerate(embeddings):
        print(f"{type_name} {i + 1} '{text_list[i]}' 的嵌入向量:")
        print(f"向量维度: {len(embedding.values)}")
        print(f"前10个值: {embedding.values[:10]}")
        print("-" * 50)


def print_similarity_matrix(matrix, row_labels, col_labels, name):
    """打印相似度矩阵"""
    print(f"\n{name}相似度矩阵:")
    for i, row in enumerate(matrix):
        for j, val in enumerate(row):
            print(f"{row_labels[i]} 与 {col_labels[j]} 的相似度: {val:.4f}")


def main():
    # 英文子句
    english_clauses = [
        "in which the everyday citizens in these communities",
        "contribute to the projects that are in the campaign"
    ]

    # 中文
    chinese_text = "由社区里的普通市民为这些活动中的项目出资"

    # 初始化HanLP分词器
    HanLP = HanLPClient('https://www.hanlp.com/api', auth='************************************************',
                        language='zh')
    result = HanLP.tokenize(chinese_text)
    print(result[0])  # 打印第一个分句的分词结果
    result = result[0]  # 提取第一个分句的分词结果
    print('--' * 50)

    # 创建从前向后的候选子句
    forward_candidates = []
    for i in range(2, len(result) + 1):
        sub_clause_list = result[0:i]
        sub_clause_str = ''.join(sub_clause_list)
        forward_candidates.append(sub_clause_str)

    # 创建从后向前的候选子句
    backward_candidates = []
    for i in range(2, len(result) + 1):
        sub_clause_list = result[-i:]
        sub_clause_str = ''.join(sub_clause_list)
        backward_candidates.append(sub_clause_str)

    # 打印候选子句信息
    print(f"\n从前向后总共生成了 {len(forward_candidates)} 个候选分割子句")
    for i, candidate in enumerate(forward_candidates):
        print(f"前向候选 {i + 1}: {candidate}")

    print(f"\n从后向前总共生成了 {len(backward_candidates)} 个候选分割子句")
    for i, candidate in enumerate(backward_candidates):
        print(f"后向候选 {i + 1}: {candidate}")

    # API 设置
    api_url = "https://api.siliconflow.cn/v1/embeddings"
    api_key = "sk-qmyqlcevlelaxuxuvwhkpdqsyhoadeaudrawwylzhntpuknv"

    print("\n开始处理嵌入向量...")

    # 嵌入英文子句
    print("\n处理英文子句的嵌入向量...")
    english_embeddings = get_embeddings(english_clauses, api_url, api_key)
    print_embeddings(english_embeddings, english_clauses, "英文子句")

    # 嵌入前向候选子句
    print("\n处理前向候选子句的嵌入向量...")
    forward_embeddings = get_embeddings(forward_candidates, api_url, api_key)
    print_embeddings(forward_embeddings, forward_candidates, "前向候选子句")

    # 嵌入后向候选子句
    print("\n处理后向候选子句的嵌入向量...")
    backward_embeddings = get_embeddings(backward_candidates, api_url, api_key)
    print_embeddings(backward_embeddings, backward_candidates, "后向候选子句")

    # 计算相似度矩阵
    print("\n计算相似度矩阵...")

    # 计算前向候选子句与第一个英文子句的相似度
    forward_english_sim = calculate_similarity_matrix(forward_embeddings, [english_embeddings[0]])

    # 计算后向候选子句与第二个英文子句的相似度
    backward_english_sim = calculate_similarity_matrix(backward_embeddings, [english_embeddings[1]])

    # 打印相似度矩阵
    print_similarity_matrix(
        forward_english_sim,
        forward_candidates,
        [english_clauses[0]],
        "前向候选子句与第一个英文子句"
    )

    print_similarity_matrix(
        backward_english_sim,
        backward_candidates,
        [english_clauses[1]],
        "后向候选子句与第二个英文子句"
    )

    # 找到最佳分割点
    split_idx, forward_idx, backward_idx, best_scores = find_optimal_split_enhanced(
        result, forward_candidates, backward_candidates,
        forward_english_sim, backward_english_sim
    )

    # 打印结果
    print("\n===== 结果 =====")

    if split_idx is not None:
        print(f"最佳分割点: 第{split_idx}个标记之后 (共{len(result)}个标记)")
        print(f"最佳总得分: {best_scores['total_score']:.4f}")

        # 获取最佳分割的文本
        forward_text = forward_candidates[forward_idx]
        backward_text = backward_candidates[backward_idx]

        print(f"前向部分: '{forward_text}'")
        print(f"后向部分: '{backward_text}'")

        print("\n得分详情:")
        print(f"前向部分与第一个英文子句的相似度: {best_scores['forward_score']:.4f}")
        print(f"后向部分与第二个英文子句的相似度: {best_scores['backward_score']:.4f}")


        # 尝试不同的分割点并比较得分
        print("\n===== 分割点比较 =====")
        print("排名\t分割点\t前向文本\t\t\t\t\t\t\t\t\t后向文本\t\t\t\t\t\t总得分")

        # 收集所有有效分割点的得分
        all_splits = []
        for alt_split_idx in range(1, len(result)):
            alt_forward_tokens = result[:alt_split_idx]
            alt_backward_tokens = result[alt_split_idx:]

            if len(alt_forward_tokens) < 2 or len(alt_backward_tokens) < 2:
                continue

            alt_forward_idx = len(alt_forward_tokens) - 2
            alt_backward_idx = len(alt_backward_tokens) - 2

            if (alt_forward_idx < 0 or alt_forward_idx >= len(forward_english_sim) or
                    alt_backward_idx < 0 or alt_backward_idx >= len(backward_english_sim)):
                continue

            # 计算得分
            alt_scores = {}
            alt_scores['forward_score'] = forward_english_sim[alt_forward_idx, 0]
            alt_scores['backward_score'] = backward_english_sim[alt_backward_idx, 0]
            alt_scores['total_score'] = alt_scores['forward_score'] + alt_scores['backward_score']

            all_splits.append({
                'split_idx': alt_split_idx,
                'forward_idx': alt_forward_idx,
                'backward_idx': alt_backward_idx,
                'forward_text': ''.join(alt_forward_tokens),
                'backward_text': ''.join(alt_backward_tokens),
                'scores': alt_scores
            })

        # 按总得分排序
        all_splits.sort(key=lambda x: x['scores']['total_score'], reverse=True)

        # 打印前5个最佳分割点
        for i, split_info in enumerate(all_splits[:5]):
            print(
                f"{i + 1}\t\t{split_info['split_idx']}\t\t{split_info['forward_text']}\t\t\t\t\t{split_info['backward_text']}\t\t\t{split_info['scores']['total_score']:.4f}")
    else:
        print("找不到有效的分割点。")


main()